#!/bin/bash

# Start port forwarding for all PostgreSQL database instances
# Run in background to allow access from localhost

set -e

# Define all AI tools and their corresponding ports (matching .env configuration)
declare -A AI_TOOLS_PORTS=(
    ["augment"]="5433"
    ["roo"]="5434" 
    ["cline"]="5435"
    ["cursor"]="5436"
    ["copilot"]="5437"
    ["windsurf"]="5438"
    ["cursor-max"]="5439"
    ["cursor-gemini"]="5440"
    ["cursor-gemini-max"]="5441"
)

# Namespace
NAMESPACE="llm-eval"

echo "🔗 Starting port forwarding for all PostgreSQL database instances..."

# Kill any existing port forwards for these services
echo "🧹 Cleaning up existing port forwards..."
pkill -f 'kubectl port-forward.*postgresql-.*-service' 2>/dev/null || true

# Wait a moment for cleanup
sleep 2

echo "📋 Setting up port forwarding for all databases:"

for tool in "${!AI_TOOLS_PORTS[@]}"; do
    port="${AI_TOOLS_PORTS[$tool]}"
    service_name="postgresql-${tool}-service"
    
    echo "  🔌 Starting port forward for $tool: localhost:$port -> $service_name:5432"
    
    # Check if service exists before attempting port forward
    if kubectl get service -n $NAMESPACE $service_name >/dev/null 2>&1; then
        kubectl port-forward -n $NAMESPACE service/$service_name $port:5432 &
        
        # Store PID for cleanup
        echo $! > /tmp/port-forward-${tool}.pid
        echo "    ✅ Port forward started for $tool (PID: $!)"
    else
        echo "    ⚠️  Service $service_name not found, skipping..."
    fi
done

echo "  ⏳ Waiting for port forwards to establish..."
sleep 5

echo ""
echo "🎉 Port forwarding setup completed!"
echo ""
echo "📝 Database connection information:"
echo "   Format: postgresql://postgres:postgres123@localhost:PORT/DATABASE_NAME"
echo ""

for tool in "${!AI_TOOLS_PORTS[@]}"; do
    port="${AI_TOOLS_PORTS[$tool]}"
    db_name="llm_eval_${tool//-/_}"  # Replace hyphens with underscores for DB name
    echo "  📊 $tool:"
    echo "     Port: localhost:$port"
    echo "     DB: $db_name"
    echo "     URL: postgresql://postgres:postgres123@localhost:$port/$db_name"
    echo ""
done

echo "🔗 Port forwarding is running in background"
echo "📋 To stop all port forwarding, run: pkill -f 'kubectl port-forward.*postgresql-.*-service'"
echo "📋 PID files stored in: /tmp/port-forward-*.pid"
echo ""
echo "💡 To test a connection (example for cursor):"
echo "   psql postgresql://postgres:postgres123@localhost:5436/llm_eval_cursor"
echo ""
echo "🔍 To check running port forwards:"
echo "   ps aux | grep 'kubectl port-forward' | grep -v grep" 