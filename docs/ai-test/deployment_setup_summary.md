# LLM Evaluation Platform Deployment Setup Summary

**Date**: 2025-05-29  
**Objective**: Create independent testing environments for different AI tools to compare their capabilities

---

## 📋 Project Background

**Core Objective**: Test and compare the capabilities of different AI code assistant tools:
- Augment Code
- Roo Code  
- Cline
- Cursor
- GitHub Copilot
- Windsurf

**Testing Strategy**: Create independent database instances for each tool, with all databases downgraded to the same baseline version (`28b5937d928a`), ensuring a fair comparison.

---

## ✅ Work Completed Today

### 1. **GitHub Self-hosted Runner Setup**
- **Problem**: GitHub Actions run in the cloud by default, unable to access the local k3s cluster
- **Solution**: Configure a Self-hosted Runner to run in the local WSL2 environment
- **Steps**:
  ```bash
  # Download and configure GitHub Runner
  curl -o actions-runner-linux-x64-2.324.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.324.0/actions-runner-linux-x64-2.324.0.tar.gz
  tar xzf ./actions-runner-linux-x64-2.324.0.tar.gz
  ./config.sh --url https://github.com/everestaiteam2025/llm-eval-platform --token BR72KOXLNIFHFJBKNODJ2VTIHC7IE
  ```
- **Result**: ✅ Runner successfully connected to GitHub, listening for tasks

### 2. **Kubernetes Cluster Verification**
- **Environment**: Windows Rancher Desktop + WSL2 Ubuntu
- **Verification Steps**:
  ```bash
  kubectl cluster-info
  kubectl get nodes
  kubectl get namespaces
  ```
- **Result**: ✅ k3s cluster running normally

### 3. **PostgreSQL Database Deployment**
- **Deployment Resources**:
  - Namespace: `llm-eval`
  - PersistentVolumeClaim: `postgresql-pvc` (10Gi)
  - Secret: `postgresql-secret` (username/password)
  - Deployment: `postgresql` (postgres:15-alpine)
  - Service: `postgresql-service` (ClusterIP)
- **Verification**:
  ```bash
  kubectl run psql-test --rm -it --image=postgres:15-alpine --restart=Never --namespace=llm-eval -- psql *********************************************************/llm_eval -c "SELECT version();"
  ```
- **Result**: ✅ PostgreSQL 15.12 running normally, network connection normal

### 4. **Docker Image Build and Optimization**

#### Backend Image Issue Resolution
- **Problem**: Poetry virtual environment configuration caused multi-stage build failure
- **Solution**: Simplify to single-stage build, use system Python
- **Key Configuration**:
  ```dockerfile
  ENV POETRY_VIRTUALENVS_CREATE=false
  RUN poetry install --only=main --no-root
  ```
- **Result**: ✅ Backend image built successfully

#### Frontend Image Issue Resolution
- **Problem**: nginx permission error - "Permission denied" for `/run/nginx.pid`
- **Solution**: 
  - Modify `nginx.conf`: `pid /tmp/nginx.pid;`
  - Adjust Dockerfile permission settings
- **Result**: ✅ Frontend image built successfully

### 5. **Database Migration Configuration Fix**
- **Problem**: Alembic used a hardcoded localhost database URL
- **Solution**: Modify `alembic/env.py` to prioritize environment variables
- **Code Modification**:
  ```python
  # Before
  db_url = config.get_main_option("sqlalchemy.url")
  
  # After  
  db_url = os.environ.get("DATABASE_URL") or config.get_main_option("sqlalchemy.url")
  ```
- **Result**: ✅ Successfully ran all migrations to the latest version

### 6. **Full Application Stack Deployment**
- **Component Status**:
  - PostgreSQL: ✅ Running normally
  - Backend (2 replicas): ✅ Running normally, API responding normally
  - Frontend (2 replicas): ✅ Running normally, nginx configured correctly
- **Service Exposure**:
  - Frontend: LoadBalancer `*************:3000`
  - Backend: ClusterIP (internal access)
  - PostgreSQL: ClusterIP (internal access)

### 7. **Health Check Verification**
```json
{
  "status": "healthy",
  "database": {
    "connection": {"status": "healthy"},
    "tables": {"status": "healthy", "message": "Tasks table accessible (0 tasks)"}
  },
  "services": {
    "api": {"status": "healthy"}
  }
}
```

### 8. **Local Docker Registry Deployment** ✅ **NEW**
- **Objective**: Resolve network bottleneck issue with pushing to GitHub Registry in CI/CD
- **Implementation**:
  - Create local Docker Registry (`localhost:5000`)
  - Modify CI/CD pipeline to use local registry
  - Configure k3s to trust local registry
  - Create registry management and testing scripts
- **Performance Improvement**:
  - Build + push speed increased 7-10x
  - From ~450 seconds down to ~60 seconds
- **Tools**:
  - `scripts/manage-local-registry.sh` - Registry management
  - `scripts/test-local-registry.sh` - Full test process
  - `docs/LOCAL_REGISTRY.md` - Detailed documentation

### 9. **Multi-instance PostgreSQL Environment** ✅ **NEW**
- **Objective**: Create independent database instances for each AI tool
- **Implementation**:
  - Deploy 6 independent PostgreSQL instances
  - Each instance has its own PVC, Secret, Service
  - Database naming: `llm_eval_augment`, `llm_eval_roo`, `llm_eval_cline`, `llm_eval_cursor`, `llm_eval_copilot`, `llm_eval_windsurf`
- **Configuration File**: `k8s/postgresql-multi-instances.yaml`
- **Management Script**: `scripts/setup-multi-databases.sh`

---

## 🐛 Major Problems Encountered and Solutions

### 1. **Poetry Virtual Environment Issue**
- **Error**: `COPY --from=builder /app/.venv /app/.venv: not found`
- **Cause**: `POETRY_VENV_IN_PROJECT=1` did not correctly create the .venv directory
- **Solution**: Use `POETRY_VIRTUALENVS_CREATE=false` to install directly to system Python

### 2. **Nginx Permission Issue**
- **Error**: `nginx: [emerg] open() "/run/nginx.pid" failed (13: Permission denied)`
- **Cause**: Non-root user cannot write to `/run` directory
- **Solution**: Change PID file to `/tmp/nginx.pid` and set correct permissions

### 3. **Database Connection Issue**
- **Error**: Alembic connected to localhost instead of postgresql-service
- **Cause**: Hardcoded database URL in `alembic.ini`
- **Solution**: Modify `env.py` to prioritize environment variable `DATABASE_URL`

### 4. **Container Image Pull Policy**
- **Problem**: Kubernetes tried to pull locally built images from remote
- **Solution**: Set `imagePullPolicy: Never` to use local images

---

## 📊 Current Status

### Tech Stack Versions
- **Kubernetes**: k3s v1.31.5 (Rancher Desktop)
- **PostgreSQL**: 15.12 
- **Python**: 3.12
- **Node.js**: 20
- **Database Schema**: `d70dbb5a6738` (latest version)

### Deployment Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   PostgreSQL    │
│   (nginx)       │───▶│   (FastAPI)     │───▶│   (postgres)    │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 5432    │
│   LoadBalancer  │    │   ClusterIP     │    │   ClusterIP     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Resource Configuration
- **Namespace**: `llm-eval`
- **Persistent Storage**: 10Gi (local-path)
- **Replica Count**: Frontend(2), Backend(2), PostgreSQL(1)

---

## 🚀 Next Steps

### 1. **Database Baseline Setup** ⏳ **IN PROGRESS**
- [x] Create independent PostgreSQL instances for each AI tool ✅ **COMPLETED**
- [x] Configure database naming convention ✅ **COMPLETED**
- [x] Execute `alembic downgrade 28b5937d928a` on all test databases ✅ **COMPLETED**
- [x] Verify consistency of all database schemas ✅ **COMPLETED**

### 2. **Local Development Environment Optimization** ✅ **COMPLETED**
- [x] Deploy local Docker Registry ✅ **COMPLETED**
- [x] Modify CI/CD pipeline to use local registry ✅ **COMPLETED**
- [x] Create registry management tools ✅ **COMPLETED**
- [x] Performance testing and documentation ✅ **COMPLETED**

### 3. **GitHub Actions Integration Testing**
- [x] Test CI/CD flow with Self-hosted Runner ✅ **COMPLETED**
- [x] Configure local registry integration ✅ **COMPLETED**

### 4. **AI Tool Testing**
- [x] Design standardized test tasks (refer to Usage Statistics feature implementation challenge) ✅ **COMPLETED**
- [x] Prepare independent branches for each AI tool ✅ **COMPLETED**

---

## 📝 Configuration File Checklist

### Kubernetes Configuration
- `k8s/namespace.yaml` - Namespace definition
- `k8s/postgresql.yaml` - PostgreSQL deployment configuration
- `k8s/backend.yaml` - Backend application configuration  
- `k8s/frontend.yaml` - Frontend application configuration

### Docker Configuration
- `backend/Dockerfile` - Backend image build
- `frontend/Dockerfile` - Frontend image build
- `frontend/nginx.conf` - Nginx configuration

### CI/CD Configuration
- `.github/workflows/ci-cd.yaml` - GitHub Actions workflow (modified to use self-hosted runner)

### Database Configuration
- `backend/alembic.ini` - Alembic configuration
- `backend/alembic/env.py` - Alembic environment configuration (modified to support environment variables)

---

## 🔧 Useful Commands

### Kubernetes Operations
```bash
# View status of all resources
kubectl get all -n llm-eval

# View Pod logs
kubectl logs -l app=backend -n llm-eval --tail=50

# Execute database migration
kubectl exec -n llm-eval deployment/backend -- poetry run alembic upgrade head

# Test application health status
kubectl exec -n llm-eval deployment/backend -- curl -s http://localhost:8000/api/v1/health
```

### Docker Operations
```bash
# Build images
docker build -t llm-eval-backend:latest ./backend
docker build -t llm-eval-frontend:latest ./frontend

# View images
docker images | grep llm-eval
```

### GitHub Runner
```bash
# Check Runner status
ps aux | grep Runner.Listener

# Restart Runner (if needed)
cd /home/<USER>/actions-runner && ./run.sh
```

---

## 💡 Lessons Learned

1. **Containerized Permission Management**: Pay special attention to file permissions and directory access rights when running services as non-root users.
2. **Kubernetes Networking**: Use Service names for inter-service communication, not localhost.
3. **Environment Variable Priority**: Application configuration should prioritize environment variables for easier containerized deployment.
4. **Image Pull Policy**: Use `imagePullPolicy: Never` during local development to avoid remote pulls.
5. **Database Migration**: Ensure the migration tool uses the same database connection configuration as the application.

---

**Status**: 🟢 Basic deployment environment is ready, can proceed with AI tool testing 