import { useEffect, useState, useMemo } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import useTaskStore from '../store/taskStore';
import OutputDisplay from '../components/OutputDisplay';
import EvaluateArea from '../components/EvaluateArea';
import ReportViewer from '../components/ReportViewer';
import LoadingSpinner from '../components/LoadingSpinner';
import PromptDisplay from '../components/PromptDisplay';
import { Copy as CopyIcon, Check, DownloadCloud, BarChart2, Users, Maximize2, Minimize2 } from 'lucide-react';
import { AggregationAlgorithmEnum, Evaluation, EvaluationReportResponse, Generation } from '../api/apiClient';
import AggregatedResultsChart from '../components/AggregatedResultsChart';
import EvaluatorConsistencyTable from '../components/EvaluatorConsistencyTable';
import { useTheme } from '../context/ThemeContext';

interface ViewTaskPageProps {
  taskId: number;
}

const CACHE_VALIDITY_MS = 5 * 60 * 1000; // 5 minutes

// ADDED: Helper to get a map from blind_id to shortened model name
const getBlindIdToModelNameMap = (generations: Generation[]): Record<string, string> => {
  const map: Record<string, string> = {};
  generations.forEach(gen => {
    if (gen.blind_id) {
      const modelName = gen.model_id_used.split('/').pop() || gen.model_id_used;
      map[gen.blind_id] = modelName;
    }
  });
  return map;
};

interface CustomCodeProps {
  node?: unknown;
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const PromptCustomCodeRenderer: React.FC<CustomCodeProps> = ({ inline, className, children, ...props }) => {
  const { resolvedTheme } = useTheme();
  const [copied, setCopied] = useState(false);
  const match = /language-(\w+)/.exec(className || '');
  const lang = match ? match[1] : 'text';

  const handleCopy = () => {
    const codeToCopy = String(children).replace(/\n$/, '');
    navigator.clipboard.writeText(codeToCopy);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const syntaxTheme = resolvedTheme === 'dark' ? vscDarkPlus : vs;

  if (inline || (!match && typeof children === 'string' && !children.includes('\n'))) {
    return (
      <code 
        {...props} 
        className="bg-light-component dark:bg-dark-component-subtle border border-light-border dark:border-dark-border text-light-accent dark:text-dark-accent px-1.5 py-0.5 mx-0.5 rounded font-mono text-[0.9em] font-medium transition-colors duration-300"
      >
        {children}
      </code>
    );
  }

  if (!inline && match) {
    return (
      <div className="code-block-container relative group bg-light-background dark:bg-dark-component-subtle rounded-md my-2 shadow-md border border-light-border dark:border-dark-border transition-colors duration-300">
        <div className="code-block-header flex justify-between items-center px-3 py-1.5 bg-light-component dark:bg-dark-component border-b border-light-border dark:border-dark-border rounded-t-md transition-colors duration-300">
          <span className="text-xs text-light-secondary dark:text-dark-secondary font-mono select-none">{lang}</span>
          <button 
            onClick={handleCopy}
            className="text-xs text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors p-1 rounded opacity-60 group-hover:opacity-100"
            title="Copy code"
          >
            {copied ? <Check size={16} className="text-green-500" /> : <CopyIcon size={16} />}
          </button>
        </div>
        <SyntaxHighlighter
          {...props}
          style={syntaxTheme}
          language={lang}
          PreTag="div"
          className="!p-3 !m-0 !bg-transparent !text-sm overflow-x-auto rounded-b-md custom-scrollbar"
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      </div>
    );
  } 
  
  if (!inline) {
    return (
      <div className="code-block-container relative group bg-light-background dark:bg-dark-component-subtle border border-light-border dark:border-dark-border rounded-md my-2 shadow transition-colors duration-300">
         <div className="code-block-header flex justify-end items-center px-3 py-1.5 bg-light-component dark:bg-dark-component border-b border-light-border dark:border-dark-border rounded-t-md transition-colors duration-300">
          <button 
            onClick={handleCopy}
            className="text-xs text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors p-1 rounded opacity-60 group-hover:opacity-100"
            title="Copy code"
          >
            {copied ? <Check size={16} className="text-green-500" /> : <CopyIcon size={16} />}
          </button>
        </div>
        <pre className="!p-3 !m-0 !bg-transparent text-sm overflow-x-auto rounded-b-md custom-scrollbar whitespace-pre-wrap text-light-primary dark:text-dark-primary transition-colors duration-300">
          <code {...props} className={className}>
            {children}
          </code>
        </pre>
      </div>
    );
  }
  return (
    <code {...props} className={`${className || ''} bg-light-component dark:bg-dark-component-subtle border border-light-border dark:border-dark-border text-light-accent dark:text-dark-accent px-1.5 py-0.5 mx-0.5 rounded font-mono text-[0.9em] font-medium transition-colors duration-300`}>
      {children}
    </code>
  );
};

function ViewTaskPage({ taskId }: ViewTaskPageProps) {
  console.log('[ViewTaskPage] Component rendered. Received taskId prop:', taskId);

  const { 
    tasks, 
    availableModels, 
    loadTaskDetails, 
    evaluateTask,
    fetchAggregatedReport
  } = useTaskStore();
  
  // Ensure tasks[taskId] access is safe if taskId might not be in tasks yet
  const taskState = tasks && taskId ? tasks[taskId] : undefined;
  console.log('[ViewTaskPage] Current taskState from store for taskId ', taskId, ':', taskState);

  const [selectedEvaluationId, setSelectedEvaluationId] = useState<number | null>(null);
  const [selectedEvaluatorInBatch, setSelectedEvaluatorInBatch] = useState<string | null>(null);
  const [selectedOutputModelIndex, setSelectedOutputModelIndex] = useState<number>(0);
  const [isOutputCopied, setIsOutputCopied] = useState(false);
  const [isReportCopied, setIsReportCopied] = useState(false);
  const [isAllOutputsCopied, setIsAllOutputsCopied] = useState(false);
  const [isAllReportsCopied, setIsAllReportsCopied] = useState(false);
  const [selectedAggregationAlgorithm, setSelectedAggregationAlgorithm] = useState<AggregationAlgorithmEnum>(
    AggregationAlgorithmEnum.AVERAGE_RANK
  );
  const [expandedAlgorithmDescription, setExpandedAlgorithmDescription] = useState<AggregationAlgorithmEnum | null>(null);
  const [evaluatorWeightsInput, setEvaluatorWeightsInput] = useState<Record<string, string>>({});
  const [revealGlobalReasoning, setRevealGlobalReasoning] = useState(false);
  const [showScrollbar, setShowScrollbar] = useState(true);
  const [hasAutoSwitched, setHasAutoSwitched] = useState<Set<number>>(new Set());
  
  // ALL HOOKS MUST BE CALLED BEFORE ANY EARLY RETURN
  // Helper to get display ID for a generation based on a specific evaluation context
  const getDisplayIdForGenerationInEvaluation = (generationId: number, evaluationId: number | null): string => {
    if (!taskState) return `Gen ID: ${generationId}`;
    const generation = taskState.outputs.find(g => g.id === generationId);
    if (!generation) return `Unknown Gen ID: ${generationId}`;

    const evaluation = taskState.evaluations?.find((ev: Evaluation) => ev.id === evaluationId);
    const currentEvalUsedBlindIds = evaluation ? evaluation.evaluation_used_blind_ids : false;
    
    const modelName = generation.model_id_used.split('/').pop() || generation.model_id_used;

    if (currentEvalUsedBlindIds) {
      return `${modelName} (blind_id: ${generation.blind_id})`;
    }
    return modelName;
  };

  useEffect(() => {
    console.log('[ViewTaskPage] useEffect (data loading) triggered. taskId:', taskId, 'taskState exists:', !!taskState);
    if (taskId && !isNaN(taskId) && taskId > 0) {
      if (!taskState || 
          !taskState.cacheTimestamp || 
          (Date.now() - taskState.cacheTimestamp > CACHE_VALIDITY_MS) ||
          !taskState.evaluations) { // Also check if evaluations are missing, crucial for older tasks
        console.log('[ViewTaskPage] Conditions met (no taskState, stale cache, or missing evaluations). Calling loadTaskDetails for taskId:', taskId);
        loadTaskDetails(taskId);
      } else {
        console.log('[ViewTaskPage] Using cached taskState. Timestamp:', taskState.cacheTimestamp, 'Evaluations present:', !!taskState.evaluations);
      }
    } else {
      if (!taskId || isNaN(taskId) || taskId <= 0) {
        console.log('[ViewTaskPage] Not calling loadTaskDetails because taskId is invalid or missing:', taskId);
      }
    }
  }, [taskId, taskState, loadTaskDetails]); // taskState is included to re-evaluate if it changes from undefined to defined by other means, though loadTaskDetails is the primary mechanism
  
  useEffect(() => {
    // Set initial selected evaluation and then evaluator within that batch
    console.log('[ViewTaskPage] useEffect (selection logic) triggered. taskState?.evaluations:', taskState?.evaluations, 'selectedEvaluationId:', selectedEvaluationId, 'selectedEvaluatorInBatch:', selectedEvaluatorInBatch);
    if (taskState?.evaluations && taskState.evaluations.length > 0) {
      let currentSelectedEvalId = selectedEvaluationId;
      if (!currentSelectedEvalId || !taskState.evaluations.find(ev => ev.id === currentSelectedEvalId)) {
        const latestEvaluation = taskState.evaluations.reduce((latest, current) => (current.id > latest.id ? current : latest), taskState.evaluations[0]);
        console.log('[ViewTaskPage] Defaulting selectedEvaluationId to latest:', latestEvaluation.id);
        setSelectedEvaluationId(latestEvaluation.id);
        currentSelectedEvalId = latestEvaluation.id;
      }

      if (currentSelectedEvalId) {
        const currentBatch = taskState.evaluations.find(ev => ev.id === currentSelectedEvalId);
        if (currentBatch && currentBatch.rankings.length > 0) {
          const evaluatorsInBatch = Array.from(new Set(currentBatch.rankings.map(r => r.evaluator_model_id)));
          if (evaluatorsInBatch.length > 0) {
            if (!selectedEvaluatorInBatch || !evaluatorsInBatch.includes(selectedEvaluatorInBatch)) {
              console.log('[ViewTaskPage] Defaulting selectedEvaluatorInBatch to first in batch:', evaluatorsInBatch[0]);
              setSelectedEvaluatorInBatch(evaluatorsInBatch[0]);
            }
          } else {
            if (selectedEvaluatorInBatch !== null) {
                 console.log('[ViewTaskPage] Resetting selectedEvaluatorInBatch to null (no evaluators in batch rankings).');
                 setSelectedEvaluatorInBatch(null);
            }
          }
        } else {
          if (selectedEvaluatorInBatch !== null) {
            console.log('[ViewTaskPage] Resetting selectedEvaluatorInBatch to null (no rankings in current batch).');
            setSelectedEvaluatorInBatch(null);
          }
        }
      }
    } else {
      if (selectedEvaluationId !== null || selectedEvaluatorInBatch !== null) {
        console.log('[ViewTaskPage] No evaluations in taskState, resetting selections.');
        setSelectedEvaluationId(null);
        setSelectedEvaluatorInBatch(null);
      }
    }

    if (taskState?.outputs && selectedOutputModelIndex >= taskState.outputs.length && taskState.outputs.length > 0) {
      console.log('[ViewTaskPage] Resetting selectedOutputModelIndex to 0 due to outputs length change or invalid index.');
      setSelectedOutputModelIndex(0);
    } else if (taskState?.outputs && taskState.outputs.length === 0 && selectedOutputModelIndex !== 0) {
      // If outputs become empty, reset index to 0
      setSelectedOutputModelIndex(0);
    }
  }, [taskState?.evaluations, selectedEvaluationId, selectedEvaluatorInBatch, taskState?.outputs, selectedOutputModelIndex]);
  
  // If new evaluation is done, auto-switch to it
  useEffect(() => {
    if (taskState?.currentEvaluationId && 
        taskState?.evaluations && 
        taskState.currentEvaluationId !== selectedEvaluationId &&
        !hasAutoSwitched.has(taskState.currentEvaluationId)) {
      // Check if current evaluation is done
      const currentEvaluation = taskState.evaluations.find(ev => ev.id === taskState.currentEvaluationId);
      if (currentEvaluation && currentEvaluation.status === 'EVALUATION_DONE') {
        console.log('[ViewTaskPage] Auto-switching to newly completed evaluation:', taskState.currentEvaluationId);
        setSelectedEvaluationId(taskState.currentEvaluationId);
        setSelectedEvaluatorInBatch(null); // Reset evaluator selection, let system automatically select first
        setHasAutoSwitched(prev => {
          const newSet = new Set(prev);
          if (taskState.currentEvaluationId !== null) {
            newSet.add(taskState.currentEvaluationId);
          }
          return newSet;
        });
      }
    }
  }, [taskState?.currentEvaluationId, taskState?.evaluations, selectedEvaluationId, hasAutoSwitched]);
  
  // New useEffect to fetch aggregation when relevant conditions met or algorithm changes
  useEffect(() => {
    const reportForSelectedAlgo = taskState?.aggregatedReportsByAlgorithm?.[selectedAggregationAlgorithm];
    if (taskState?.currentEvaluationId && 
        taskState?.taskStatusFromBackend === 'EVALUATION_DONE' && 
        !taskState.isAggregating && 
        !reportForSelectedAlgo // Check if report for CURRENTLY selected algorithm is missing
    ) {
      // Automatically fetch if evaluation is done and no aggregation report exists *for this algorithm* yet
      // console.log(`[ViewTaskPage] Auto-fetching aggregated report for eval ${taskState.currentEvaluationId}, algo: ${selectedAggregationAlgorithm}`);
      // fetchAggregatedReport(taskId, taskState.currentEvaluationId, selectedAggregationAlgorithm);
      // Decided against auto-fetch for now to give user control, can be re-enabled.
    }
  }, [taskId, taskState?.currentEvaluationId, taskState?.taskStatusFromBackend, taskState?.isAggregating, taskState?.aggregatedReportsByAlgorithm, fetchAggregatedReport, selectedAggregationAlgorithm]);
  
  // Derive the selected output for display using useMemo
  const selectedGenerationForDisplay = useMemo(() => {
    if (taskState?.outputs && taskState.outputs.length > selectedOutputModelIndex) {
      return taskState.outputs[selectedOutputModelIndex];
    }
    return null;
  }, [taskState?.outputs, selectedOutputModelIndex]);

  // Calculate valid outputs count (outputs without errors)
  const validOutputsCount = useMemo(() => {
    if (!taskState?.outputs) return 0;
    return taskState.outputs.filter(output => output.output_text && !output.error_message).length;
  }, [taskState?.outputs]);

  // Calculate aggregated usage statistics
  const aggregatedUsage = useMemo(() => {
    if (!taskState?.outputs) return null;
    
    // Don't show aggregated usage statistics if generation is still in progress
    if (taskState.isGenerating || taskState.isStreaming) {
      return null;
    }
    
    // Only include valid outputs that have usage statistics (generation_id exists)
    const validOutputsWithUsage = taskState.outputs.filter(output => 
      output.output_text && 
      !output.error_message && 
      output.generation_id // Only include generations with usage statistics
    );
    
    if (validOutputsWithUsage.length === 0) return null;
    
    const totals = validOutputsWithUsage.reduce((acc, output) => {
      return {
        prompt_tokens: acc.prompt_tokens + (output.prompt_tokens || 0),
        completion_tokens: acc.completion_tokens + (output.completion_tokens || 0),
        total_tokens: acc.total_tokens + (output.total_tokens || 0),
        reasoning_tokens: acc.reasoning_tokens + (output.reasoning_tokens || 0),
        cached_tokens: acc.cached_tokens + (output.cached_tokens || 0),
        cost_credits: acc.cost_credits + (output.cost_credits || 0),
        count: acc.count + 1,
        totalGenerations: taskState.outputs.filter(o => o.output_text && !o.error_message).length
      };
    }, {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0,
      reasoning_tokens: 0,
      cached_tokens: 0,
      cost_credits: 0,
      count: 0,
      totalGenerations: 0
    });
    
    return totals.count > 0 ? totals : null;
  }, [taskState?.outputs, taskState?.isGenerating, taskState?.isStreaming]);

  // ADDED: Helper to get a map from blind_id to shortened model name
  const blindIdToModelNameMap = useMemo(() => {
    if (taskState?.outputs) {
      return getBlindIdToModelNameMap(taskState.outputs);
    }
    return {};
  }, [taskState?.outputs]);

  // MODIFIED: Find the full Evaluation object based on selectedEvaluationId
  const fullEvaluationToShow = selectedEvaluationId && taskState?.evaluations
    ? taskState.evaluations.find(ev => ev.id === selectedEvaluationId) || null
    : null;

  // MODIFIED: reportToShowForViewer now filters by selectedEvaluatorInBatch if it's set
  const reportToShowForViewer: EvaluationReportResponse | null = fullEvaluationToShow
  ? {
      evaluation_id: fullEvaluationToShow.id,
      task_id: fullEvaluationToShow.task_id,
      status: fullEvaluationToShow.status,
      rankings: selectedEvaluatorInBatch 
                  ? fullEvaluationToShow.rankings.filter(r => r.evaluator_model_id === selectedEvaluatorInBatch)
                  : fullEvaluationToShow.rankings, // Show all if no specific evaluator is selected in batch
    }
  : null;

  // MODIFIED: Derives unique evaluators from the selected batch (fullEvaluationToShow) for weight inputs
  const uniqueEvaluatorModelIdsForWeightInputs = Array.from(new Set(
    fullEvaluationToShow?.rankings.map(r => r.evaluator_model_id) || []
  ));

  // Calculate aggregated usage statistics for evaluations in current batch
  const aggregatedEvaluationUsage = useMemo(() => {
    if (!fullEvaluationToShow?.rankings) return null;
    
    if (fullEvaluationToShow.status === 'EVALUATING' || fullEvaluationToShow.status === 'PENDING') {
      return null;
    }
    
    const allNonErroredRankingsInBatch = fullEvaluationToShow.rankings.filter(r => !r.error_message);
    const totalEvaluationsInBatch = allNonErroredRankingsInBatch.length;

    const validRankingsWithUsage = allNonErroredRankingsInBatch.filter(ranking => 
      ranking.generation_id && !ranking.error_message // Ensure generation_id exists for usage
    );
    
    // This variable was named 'totals' in the original code. Renaming to 'usageStats' for clarity.
    const usageStats = validRankingsWithUsage.reduce((acc, ranking) => ({
      prompt_tokens: acc.prompt_tokens + (ranking.prompt_tokens || 0),
      completion_tokens: acc.completion_tokens + (ranking.completion_tokens || 0),
      total_tokens: acc.total_tokens + (ranking.total_tokens || 0),
      reasoning_tokens: acc.reasoning_tokens + (ranking.reasoning_tokens || 0),
      cached_tokens: acc.cached_tokens + (ranking.cached_tokens || 0),
      cost_credits: acc.cost_credits + (ranking.cost_credits || 0),
      count: acc.count + 1,
    }), {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0,
      reasoning_tokens: 0,
      cached_tokens: 0,
      cost_credits: 0,
      count: 0,
    });
        
    // The main card for "Evaluation Usage Summary" renders if this useMemo returns a truthy value 
    // AND (aggregatedEvaluationUsage.total_tokens > 0 || aggregatedEvaluationUsage.cost_credits !== null).
    // So, if we return an object that would satisfy that outer condition, it MUST include totalEvaluations.
    if (usageStats.count > 0 && (usageStats.total_tokens > 0 || (usageStats.cost_credits !== null && usageStats.cost_credits > 0))) {
      return {
        ...usageStats,
        totalEvaluations: totalEvaluationsInBatch 
      };
    }
    
    // If usageStats.count is 0, or if tokens and costs are all zero,
    // returning null here means the main "Evaluation Usage Summary" card won't render.
    // This will prevent errors, and the UI might fall back to the "Usage statistics are not available" message if appropriate.
    return null;
  }, [fullEvaluationToShow?.rankings, fullEvaluationToShow?.status]);

  // NOW CHECK FOR EARLY RETURN - ALL HOOKS MUST BE ABOVE THIS LINE
  if (!taskState) {
    return (
      <div className="bg-light-component dark:bg-dark-component shadow-lg rounded-lg p-6 sm:p-8 mb-6 min-h-[200px] flex items-center justify-center transition-colors duration-300">
        <LoadingSpinner message={`Loading task ${taskId}...`} />
      </div>
    );
  }
  
  // Updated to match new signature from EvaluateArea and store
  const handleEvaluate = (selectedModels: string[], useBlindIdsForEval: boolean, customEvaluationPrompt?: string) => {
    evaluateTask(taskId, selectedModels, useBlindIdsForEval, customEvaluationPrompt);
  };

  const handleCopyAllOutputs = () => {
    if (!taskState?.outputs || taskState.outputs.length === 0) return;

    let allOutputsString = `Task ID: ${taskId}\nPrompt: ${taskState.prompt}\n\n---\n\n`;

    taskState.outputs.forEach(output => {
      const modelDisplayName = output.model_id_used.split('/').pop() || output.model_id_used;
      allOutputsString += `Model: ${modelDisplayName}\n`;
      if (output.output_text) {
        allOutputsString += `Output:\n${output.output_text}\n`;
      }
      if (output.reasoning_text) {
        allOutputsString += `Reasoning:\n${output.reasoning_text}\n`;
      }
      if (output.error_message) {
        allOutputsString += `Error: ${output.error_message}\n`;
      }
      allOutputsString += `\n---\n\n`;
    });

    navigator.clipboard.writeText(allOutputsString.trim());
    setIsAllOutputsCopied(true);
    setTimeout(() => setIsAllOutputsCopied(false), 2000);
  };

  const handleCopyOutput = () => {
    const outputToCopy = taskState?.outputs[selectedOutputModelIndex]?.output_text;
    if (outputToCopy) {
      navigator.clipboard.writeText(outputToCopy);
      setIsOutputCopied(true);
      setTimeout(() => setIsOutputCopied(false), 2000);
    }
  };

  const handleToggleGlobalReasoning = () => setRevealGlobalReasoning(prev => !prev);

  // Helper function for copy logic to process reasoning text
  const getProcessedReasoningForCopy = (reasoningText: string | null, evalUsedBlindIds: boolean, currentBlindIdToModelNameMap: Record<string, string>): string => {
    if (!reasoningText) return '';
    if (evalUsedBlindIds && revealGlobalReasoning) {
      let processedText = reasoningText;
      for (const blindId in currentBlindIdToModelNameMap) {
        const modelName = currentBlindIdToModelNameMap[blindId];
        // Simple string replacement for plain text copy
        const regex = new RegExp(`\\b${blindId.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')}(?![-_a-zA-Z0-9])`, 'g');
        processedText = processedText.replace(regex, modelName); 
      }
      return processedText;
    }
    return reasoningText;
  };

  const handleCopyAllEvaluationReports = () => {
    // MODIFIED: This function now copies all reports from the SELECTED BATCH ONLY.
    if (!fullEvaluationToShow || !taskState?.outputs) return;

    let allReportsString = `Task ID: ${taskId} - Evaluation Batch ID: ${fullEvaluationToShow.id}\n`;
    allReportsString += `Batch Status: ${fullEvaluationToShow.status}\n\n---\n\n`;
    
    const shouldUseBlindIdsForThisBatch = fullEvaluationToShow.evaluation_used_blind_ids;

    // Iterate over all rankings (all evaluators) within this specific selected batch
    fullEvaluationToShow.rankings.forEach(ranking => {
      const evaluatorDisplayName = ranking.evaluator_model_id.split('/').pop() || ranking.evaluator_model_id;
      allReportsString += `Evaluator: ${evaluatorDisplayName}\n`;
      
      const processedReasoning = getProcessedReasoningForCopy(ranking.reasoning_text, shouldUseBlindIdsForThisBatch, blindIdToModelNameMap);
      if (processedReasoning) {
        allReportsString += `Reasoning: ${processedReasoning}\n`;
      }

      if (ranking.ranked_list_json && ranking.ranked_list_json.length > 0) {
        allReportsString += "Rankings:\n";
        ranking.ranked_list_json.forEach((gen_id, index) => {
          // Use getDisplayIdForGenerationInEvaluation for consistency with single report copy and display
          const identifierToShow = getDisplayIdForGenerationInEvaluation(gen_id, fullEvaluationToShow.id);
          allReportsString += `  Rank ${index + 1}: ${identifierToShow}\n`;
        });
      } else {
        allReportsString += "  No ranked models found for this specific ranking entry.\n";
      }
      if (ranking.error_message) {
        allReportsString += `Error: ${ranking.error_message}\n`;
      }
      allReportsString += `\n---\n\n`;
    });

    navigator.clipboard.writeText(allReportsString.trim());
    setIsAllReportsCopied(true);
    setTimeout(() => setIsAllReportsCopied(false), 2000);
  };

  const handleCopyEvaluationReport = () => {
    if (!reportToShowForViewer || !fullEvaluationToShow) return;

    const shouldUseBlindIds = fullEvaluationToShow.evaluation_used_blind_ids;

    let reportString = `Task ID: ${taskId}\nEvaluation ID: ${selectedEvaluationId}\n`;
    reportString += `Status: ${fullEvaluationToShow.status}\n\n---\n\n`;

    reportToShowForViewer.rankings.forEach(ranking => {
      const evaluatorDisplayName = ranking.evaluator_model_id.split('/').pop() || ranking.evaluator_model_id;
      reportString += `Evaluator: ${evaluatorDisplayName}\n`;

      const processedReasoning = getProcessedReasoningForCopy(ranking.reasoning_text, shouldUseBlindIds, blindIdToModelNameMap);
      if (processedReasoning) {
        reportString += `Reasoning: ${processedReasoning}\n`;
      }

      if (ranking.ranked_list_json && ranking.ranked_list_json.length > 0) {
        reportString += "Rankings:\n";
        ranking.ranked_list_json.forEach((gen_id, index) => {
          const generation = taskState.outputs.find(g => g.id === gen_id);
          let identifierToShow = `Gen ID: ${gen_id}`; 
          if (generation) {
            identifierToShow = getDisplayIdForGenerationInEvaluation(gen_id, selectedEvaluationId);
          }
          reportString += `  Rank ${index + 1}: ${identifierToShow}\n`;
        });
      } else {
        reportString += "  No ranked models found for this specific ranking entry.\n";
      }
      if (ranking.error_message) {
        reportString += `Error: ${ranking.error_message}\n`;
      }
      reportString += `\n---\n\n`;
    });

    navigator.clipboard.writeText(reportString.trim());
    setIsReportCopied(true);
    setTimeout(() => setIsReportCopied(false), 2000);
  };

  const handleAggregationAlgorithmChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedAggregationAlgorithm(event.target.value as AggregationAlgorithmEnum);
  };

  const handleFetchAggregation = () => {
    if (selectedEvaluationId && taskId) { // USE selectedEvaluationId and ensure taskId is also available
      let weights: Record<string, number> | undefined = undefined;
      if (selectedAggregationAlgorithm === AggregationAlgorithmEnum.WEIGHTED_AVERAGE_RANK) {
        weights = {};
        for (const modelId in evaluatorWeightsInput) {
          const parsedWeight = parseFloat(evaluatorWeightsInput[modelId]);
          if (!isNaN(parsedWeight) && parsedWeight > 0) { // Ensure valid, positive weight
            weights[modelId] = parsedWeight;
          } else if (evaluatorWeightsInput[modelId].trim() !== "") {
            // Optionally handle invalid input, e.g. show an error, or ignore
            console.warn(`Invalid weight for ${modelId}: ${evaluatorWeightsInput[modelId]}. Using default weight 1.0`);
            weights[modelId] = 1.0; // Default to 1.0 if input is invalid but not empty
          }
          // If input is empty, it will be omitted from weights, and backend defaults to 1.0
        }
        if (Object.keys(weights).length === 0) weights = undefined; // Send undefined if no valid weights entered
      }
      // PASS selectedEvaluationId to the store action
      fetchAggregatedReport(taskId, selectedEvaluationId, selectedAggregationAlgorithm, weights);
    } else {
      if (!selectedEvaluationId) {
        console.warn("[ViewTaskPage] Cannot fetch aggregation: No evaluation batch selected (selectedEvaluationId is null).");
        // Optionally, provide user feedback e.g., via a toast notification
      }
      if (!taskId) {
        console.warn("[ViewTaskPage] Cannot fetch aggregation: Task ID is missing.");
      }
    }
  };

  const handleWeightInputChange = (evaluatorModelId: string, value: string) => {
    setEvaluatorWeightsInput(prev => ({ ...prev, [evaluatorModelId]: value }));
  };

  const handleToggleAlgorithmDescription = (algorithm: AggregationAlgorithmEnum) => {
    if (algorithm === selectedAggregationAlgorithm) {
      setExpandedAlgorithmDescription(prev => prev === algorithm ? null : algorithm);
    }
  };

  // Placeholder descriptions - these can be expanded or moved to a config/service
  const algorithmDescriptions: Record<AggregationAlgorithmEnum, string> = {
    [AggregationAlgorithmEnum.AVERAGE_RANK]: 
      "Method: Each model is ranked by every evaluator. This algorithm calculates the simple average of these ranks for each model (rank 1, 2, 3, etc.).\n\nInterpretation: Lower average rank indicates better overall performance.\n\nPros: Simple to understand and compute. Intuitive.\n\nCons: Does not account for the magnitude of preference between ranks (e.g., a strong preference for rank 1 over 2 is treated the same as a weak preference). Highly sensitive to outlier rankings from a single evaluator.",
    [AggregationAlgorithmEnum.BORDA_COUNT]: 
      "Method: For each evaluator\'s ranked list of N models, points are awarded: N-1 for the 1st ranked model, N-2 for 2nd, ..., 0 for the Nth. Scores for each model are summed across all evaluators.\n\nInterpretation: Higher total Borda score indicates better overall performance.\n\nPros: Considers the entire ranking order from each evaluator. Somewhat mitigates the impact of extreme ranks for a single model by one evaluator compared to simple average rank. Often elects broadly acceptable candidates.\n\nCons: Can be influenced by irrelevant alternatives (models outside the top contenders affecting scores). Does not capture intensity of preference between ranks.",
    [AggregationAlgorithmEnum.WEIGHTED_AVERAGE_RANK]:
      "Method: Similar to Average Rank, but allows assigning different numerical weights to each evaluator, reflecting their perceived reliability or importance. Each model\'s rank from an evaluator is multiplied by that evaluator\'s weight before averaging. The final score is the sum of (rank * weight) divided by sum of weights for that model.\n\nInterpretation: Lower weighted average rank is better.\n\nPros: Allows for nuanced aggregation by giving more importance to trusted or more relevant evaluators.\n\nCons: Requires a justifiable and consistent method for assigning weights. The choice of weights can significantly influence the outcome. If no weights are specified (or all weights are equal), it defaults to the standard Average Rank.",
    [AggregationAlgorithmEnum.COPELAND_METHOD]:
      "Method: This is a pairwise comparison method. For every unique pair of models (A, B), it counts how many evaluators ranked A higher than B, and how many ranked B higher than A. If A is preferred by a majority of evaluators over B, Model A gets +1 point and Model B gets -1 point. If B is preferred over A, Model B gets +1 and Model A gets -1. If they are tied in pairwise comparison by an equal number of evaluators, both get 0 points for that specific pair.\n\nInterpretation: The Copeland score for a model is the sum of its points from all pairwise comparisons. Higher Copeland score indicates a model wins (or ties) more head-to-head matchups against other models.\n\nPros: Often considered robust and intuitive as it reflects direct majority preference in pairwise contests. Can identify a Condorcet winner (a model that beats all others in pairwise comparisons) if one exists.\n\nCons: Can result in ties, especially with many models or few evaluators. Does not consider the strength or margin of preference in individual rankings (e.g., how much better A was ranked than B by an evaluator). Information is lost when only pairwise wins/losses are counted.",
  };

  // Function to get status badge styles (consistent with HistorySidebar)
  const getStatusBadgeStyles = (status: string | undefined): string => {
    if (!status) return 'bg-light-component-subtle text-light-secondary dark:bg-dark-component-subtle dark:text-dark-secondary';
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-700 dark:bg-green-700/20 dark:text-green-300';
      case 'GENERATING':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-700/20 dark:text-blue-300';
      case 'EVALUATING':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-700/20 dark:text-yellow-300';
      case 'EVALUATION_DONE':
        return 'bg-purple-100 text-purple-700 dark:bg-purple-700/20 dark:text-purple-300';
      case 'FAILED':
        return 'bg-red-100 text-red-700 dark:bg-red-700/20 dark:text-red-300';
      case 'PENDING':
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-700/30 dark:text-neutral-300'; // Using neutral for PENDING for better dark mode visibility
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-700/30 dark:text-neutral-300';
    }
  };

  const toggleScrollbar = () => {
    setShowScrollbar(!showScrollbar);
  };

  return (
    <div className="space-y-4 max-w-6xl mx-auto">
      {taskState.errorMessage && (
        <div 
          className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-l-4 border-red-500 text-red-800 dark:text-red-200 p-3 rounded-r-md shadow-sm transition-colors duration-300"
          role="alert"
        >
          <div className="flex items-start space-x-2">
            <svg className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="font-semibold text-xs">Task Error (ID: {taskId})</p>
              <p className="text-xs mt-0.5">{taskState.errorMessage}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Task Details Card - Ultra Compact */}
      <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
        <div className="px-4 py-4">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2 mb-4">
            <div className="flex items-center space-x-2">
              <div className="p-1.5 bg-light-accent/10 dark:bg-dark-accent/10 rounded-md">
                <svg className="w-4 h-4 text-light-accent dark:text-dark-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">Task #{taskId}</h2>
            </div>
            {taskState.taskStatusFromBackend && (
              <div className="flex items-center space-x-1.5">
                <div className="w-1.5 h-1.5 rounded-full bg-current animate-pulse"></div>
                <span className={`px-2 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-full shadow-sm ${getStatusBadgeStyles(taskState.taskStatusFromBackend)}`}>
                  {taskState.taskStatusFromBackend}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Prompt Card - Ultra Compact */}
      <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
        <div className="px-4 py-4">
          
          <PromptDisplay
            title="System Prompt"
            content={taskState.system_prompt || ''}
            defaultContent="You are a helpful assistant. Please provide clear, accurate, and well-structured responses."
            showCopyButton={true}
            customCodeRenderer={PromptCustomCodeRenderer}
            defaultExpanded={false}
          />
          
          <PromptDisplay
            title="User Prompt"
            content={taskState.prompt}
            showCopyButton={true}
            customCodeRenderer={PromptCustomCodeRenderer}
            defaultExpanded={true}
          />
        </div>
      </div>
      
      {/* Generated Outputs Card - Ultra Compact */}
      <div className={`bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border ${
        taskState.isGenerating || taskState.isStreaming
          ? 'border-blue-500/70 dark:border-blue-400/70 shadow-blue-500/10 dark:shadow-blue-400/10 shadow-lg'
          : 'border-light-border/50 dark:border-dark-border/50'
      }`}>
        <div className="px-4 py-4">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3 mb-4">
            <div className="flex items-center space-x-2">
              <div className={`p-1.5 rounded-md ${
                taskState.isGenerating || taskState.isStreaming
                  ? 'bg-blue-500/20 dark:bg-blue-400/20'
                  : 'bg-blue-500/10 dark:bg-blue-400/10'
              }`}>
                <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
                {taskState.isGenerating || taskState.isStreaming ? (
                  <div className="flex items-center">
                    <span className="mr-2">Generated Outputs</span>
                    <div className="flex items-center space-x-1">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      <span className="text-xs text-blue-600 dark:text-blue-400 font-normal ml-1.5">
                        {taskState.isStreaming ? 'Streaming...' : 'Generating...'}
                      </span>
                    </div>
                  </div>
                ) : (
                  "Generated Outputs"
                )}
              </h2>
            </div>
            <div className="flex items-center gap-2 flex-wrap">
              {taskState?.outputs && taskState.outputs.length > 0 && taskState.outputs[selectedOutputModelIndex] && (
                <div className="flex items-center text-xs text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle px-2 py-1 rounded-full">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                  {new Date(taskState.outputs[selectedOutputModelIndex].created_at).toLocaleString()}
                </div>
              )}
              {taskState?.outputs && taskState.outputs.length > 0 && (
                <div className="flex items-center gap-1.5">
                  <select 
                    value={selectedOutputModelIndex}
                    onChange={(e) => setSelectedOutputModelIndex(parseInt(e.target.value))}
                    className="text-xs px-2 py-1.5 border border-light-border dark:border-dark-border rounded-md text-light-primary bg-light-component dark:text-dark-primary dark:bg-dark-component focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:border-transparent shadow-sm w-40 max-w-40 truncate"
                  >
                    {taskState.outputs.map((output, index) => (
                      <option key={output.id || `gen-model-${index}`} value={index} className="bg-light-component dark:bg-dark-component-subtle text-light-primary dark:text-dark-primary truncate">
                        {output.model_id_used.split('/').pop()}
                        {output.error_message && " (Error)"}
                      </option>
                    ))}
                  </select>
                  
                  <div className="flex items-center gap-1">
                    {taskState.outputs[selectedOutputModelIndex]?.output_text && (
                      <button
                        onClick={handleCopyOutput}
                        className="group p-2.5 text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary rounded-md hover:bg-light-accent/10 dark:hover:bg-dark-accent/10"
                        title="Copy selected output"
                      >
                        {isOutputCopied ? (
                          <Check size={16} className="text-green-500" />
                        ) : (
                          <CopyIcon size={16} />
                        )}
                      </button>
                    )}
                    <button
                      onClick={handleCopyAllOutputs}
                      className="group p-2.5 text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary rounded-md hover:bg-light-accent/10 dark:hover:bg-dark-accent/10"
                      title="Copy all generated outputs"
                    >
                      {isAllOutputsCopied ? (
                        <Check size={16} className="text-green-500" />
                      ) : (
                        <DownloadCloud size={16} />
                      )}
                    </button>
                    <button
                      onClick={toggleScrollbar}
                      className="group p-2.5 text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary rounded-md hover:bg-light-accent/10 dark:hover:bg-dark-accent/10"
                      title={showScrollbar ? "Remove scrollbar and show full content" : "Enable scrollbar"}
                    >
                      {showScrollbar ? (
                        <Maximize2 size={16} />
                      ) : (
                        <Minimize2 size={16} />
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          <OutputDisplay 
            selectedGeneration={selectedGenerationForDisplay}
            isLoading={taskState.isGenerating || taskState.isStreaming} 
            showScrollbar={showScrollbar}
          />
          
          {/* Individual Generation Usage Statistics */}
          {selectedGenerationForDisplay && (
            // Check if this generation has usage statistics available (generation_id exists)
            selectedGenerationForDisplay.generation_id ? (
              // Show usage statistics if available
              (selectedGenerationForDisplay.total_tokens !== null || 
               selectedGenerationForDisplay.cost_credits !== null || 
               selectedGenerationForDisplay.prompt_tokens !== null || 
               selectedGenerationForDisplay.completion_tokens !== null ||
               selectedGenerationForDisplay.reasoning_tokens !== null ||
               selectedGenerationForDisplay.cached_tokens !== null) && (
                <div className="mt-4 pt-4 border-t border-light-border/50 dark:border-dark-border/50">
                  <div className="bg-light-component-subtle dark:bg-dark-component-subtle rounded-md p-3 border border-light-border dark:border-dark-border">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="p-1.5 bg-blue-500/10 dark:bg-blue-400/10 rounded-md">
                        <svg className="w-3 h-3 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <h4 className="text-sm font-bold text-light-primary dark:text-dark-primary">
                        Usage Statistics
                      </h4>
                      <span className="text-xs px-2 py-1 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component dark:bg-dark-component border border-light-border/60 dark:border-dark-border/60">
                        {selectedGenerationForDisplay.model_id_used.split('/').pop()}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
                      {selectedGenerationForDisplay.prompt_tokens !== null && selectedGenerationForDisplay.prompt_tokens !== undefined && (
                        <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                          <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Prompt Tokens</div>
                          <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                            {selectedGenerationForDisplay.prompt_tokens.toLocaleString()}
                          </div>
                        </div>
                      )}
                      
                      {selectedGenerationForDisplay.completion_tokens !== null && selectedGenerationForDisplay.completion_tokens !== undefined && (
                        <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                          <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Completion Tokens</div>
                          <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                            {selectedGenerationForDisplay.completion_tokens.toLocaleString()}
                          </div>
                        </div>
                      )}
                      
                      {selectedGenerationForDisplay.total_tokens !== null && selectedGenerationForDisplay.total_tokens !== undefined && (
                        <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                          <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Total Tokens</div>
                          <div className="text-lg font-bold text-blue-600 dark:text-blue-400 font-mono">
                            {selectedGenerationForDisplay.total_tokens.toLocaleString()}
                          </div>
                        </div>
                      )}
                      
                      {selectedGenerationForDisplay.reasoning_tokens !== null && selectedGenerationForDisplay.reasoning_tokens !== undefined && (
                        <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                          <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Reasoning Tokens</div>
                          <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                            {selectedGenerationForDisplay.reasoning_tokens.toLocaleString()}
                          </div>
                        </div>
                      )}
                      
                      {selectedGenerationForDisplay.cached_tokens !== null && selectedGenerationForDisplay.cached_tokens !== undefined && (
                        <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                          <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Cached Tokens</div>
                          <div className="text-lg font-bold text-green-600 dark:text-green-400 font-mono">
                            {selectedGenerationForDisplay.cached_tokens.toLocaleString()}
                          </div>
                        </div>
                      )}
                      
                      {selectedGenerationForDisplay.cost_credits !== null && selectedGenerationForDisplay.cost_credits !== undefined && (
                        <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                          <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Cost</div>
                          <div className="text-lg font-bold text-light-accent dark:text-dark-accent font-mono">
                            {selectedGenerationForDisplay.cost_credits > 0 
                              ? `$${selectedGenerationForDisplay.cost_credits.toFixed(6)}`
                              : '$0.000000'
                            }
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {selectedGenerationForDisplay.generation_id && (
                      <div className="mt-3 pt-3 border-t border-light-border/50 dark:border-dark-border/50">
                        <div className="text-xs text-light-secondary dark:text-dark-secondary">
                          <span className="font-medium">Generation ID:</span>
                          <span className="ml-1 font-mono text-light-accent dark:text-dark-accent">{selectedGenerationForDisplay.generation_id}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )
            ) : (
              // Only show "Usage unavailable" message for completed generations without usage data
              // Don't show this message when generation is still in progress
              !taskState.isGenerating && !taskState.isStreaming && (
                <div className="mt-4 pt-4 border-t border-light-border/50 dark:border-dark-border/50">
                  <div className="bg-light-component-subtle dark:bg-dark-component-subtle rounded-md p-3 border border-light-border dark:border-dark-border">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="p-1.5 bg-gray-500/10 dark:bg-gray-400/10 rounded-md">
                        <svg className="w-3 h-3 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h4 className="text-sm font-bold text-light-primary dark:text-dark-primary">
                        Usage Statistics
                      </h4>
                      <span className="text-xs px-2 py-1 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component dark:bg-dark-component border border-light-border/60 dark:border-dark-border/60">
                        {selectedGenerationForDisplay.model_id_used.split('/').pop()}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-center py-6">
                      <div className="text-center">
                        <div className="text-sm text-light-secondary dark:text-dark-secondary mb-2">
                          Usage statistics are not available for this generation
                        </div>
                        <div className="text-xs text-light-secondary/70 dark:text-dark-secondary/70">
                          This feature was added after this generation was created
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            )
          )}
        </div>
      </div>
      
      {/* Usage Summary Card */}
      {aggregatedUsage && (aggregatedUsage.total_tokens > 0 || aggregatedUsage.cost_credits !== null) ? (
        <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
          <div className="px-4 py-4">
            <div className="flex items-center space-x-2 mb-4">
              <div className="p-1.5 bg-emerald-500/10 dark:bg-emerald-400/10 rounded-md">
                <svg className="w-4 h-4 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
                Usage Summary
              </h2>
              <span className="text-xs px-2 py-1 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle border border-light-border/60 dark:border-dark-border/60">
                {aggregatedUsage.count} model{aggregatedUsage.count !== 1 ? 's' : ''} with usage data
              </span>
              {aggregatedUsage.count < aggregatedUsage.totalGenerations && (
                <span className="text-xs px-2 py-1 rounded-full text-amber-700 dark:text-amber-300 bg-amber-100 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/60">
                  {aggregatedUsage.totalGenerations - aggregatedUsage.count} without usage data
                </span>
              )}
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
              <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Prompt Tokens</div>
                <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                  {aggregatedUsage.prompt_tokens.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Completion Tokens</div>
                <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                  {aggregatedUsage.completion_tokens.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Total Tokens</div>
                <div className="text-lg font-bold text-emerald-600 dark:text-emerald-400 font-mono">
                  {aggregatedUsage.total_tokens.toLocaleString()}
                </div>
              </div>
              
              {aggregatedUsage.reasoning_tokens !== null && aggregatedUsage.reasoning_tokens !== undefined && (
                <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Reasoning Tokens</div>
                  <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                    {aggregatedUsage.reasoning_tokens.toLocaleString()}
                  </div>
                </div>
              )}
              
              {aggregatedUsage.cached_tokens !== null && aggregatedUsage.cached_tokens !== undefined && (
                <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Cached Tokens</div>
                  <div className="text-lg font-bold text-green-600 dark:text-green-400 font-mono">
                    {aggregatedUsage.cached_tokens.toLocaleString()}
                  </div>
                </div>
              )}
              
              <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Total Cost</div>
                <div className="text-lg font-bold text-light-accent dark:text-dark-accent font-mono">
                  {aggregatedUsage.cost_credits > 0 
                    ? `$${aggregatedUsage.cost_credits.toFixed(6)}`
                    : '$ 0.000000'
                  }
                </div>
              </div>
            </div>
            
            {/* Average cost per model */}
            {aggregatedUsage.count > 1 && (
              <div className="mt-3 pt-3 border-t border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary">
                  <span className="font-medium">Average per model:</span>
                  <span className="ml-2 font-mono">
                    {Math.round(aggregatedUsage.total_tokens / aggregatedUsage.count).toLocaleString()} tokens
                  </span>
                  <span className="mx-2">•</span>
                  <span className="font-mono">
                    ${(aggregatedUsage.cost_credits / aggregatedUsage.count).toFixed(6)}
                  </span>
                </div>
              </div>
            )}
            
            {/* Note about missing usage data */}
            {aggregatedUsage.count < aggregatedUsage.totalGenerations && (
              <div className="mt-3 pt-3 border-t border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-amber-700 dark:text-amber-300 bg-amber-50 dark:bg-amber-900/10 p-2 rounded-md border border-amber-200 dark:border-amber-800/30">
                  <div className="flex items-center space-x-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Note:</span>
                    <span>
                      {aggregatedUsage.totalGenerations - aggregatedUsage.count} generation{aggregatedUsage.totalGenerations - aggregatedUsage.count !== 1 ? 's' : ''} 
                      {aggregatedUsage.totalGenerations - aggregatedUsage.count === 1 ? ' was' : ' were'} created before usage tracking was available
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : validOutputsCount > 0 && !taskState.isGenerating && !taskState.isStreaming && (
        /* Show message when no usage statistics are available for any generation - only for completed generations */
        <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
          <div className="px-4 py-4">
            <div className="flex items-center space-x-2 mb-4">
              <div className="p-1.5 bg-gray-500/10 dark:bg-gray-400/10 rounded-md">
                <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
                Usage Summary
              </h2>
            </div>
            
            <div className="flex items-center justify-center py-6">
              <div className="text-center">
                <div className="text-sm text-light-secondary dark:text-dark-secondary mb-2">
                  Usage statistics are not available for these generations
                </div>
                <div className="text-xs text-light-secondary/70 dark:text-dark-secondary/70">
                  All {validOutputsCount} generation{validOutputsCount !== 1 ? 's were' : ' was'} created before usage tracking was available
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Evaluate Outputs Card - Ultra Compact */}
      {taskState && taskState.outputs && taskState.outputs.length > 0 && !taskState.isGenerating && (
        <div className={`bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border ${
          taskState.isEvaluating || taskState.taskStatusFromBackend === 'EVALUATING' 
            ? 'border-orange-500/70 dark:border-orange-400/70 shadow-orange-500/10 dark:shadow-orange-400/10 shadow-lg' 
            : 'border-light-border/50 dark:border-dark-border/50'
        }`}>
          <div className="px-4 py-4">
            <div className="flex items-center space-x-2 mb-4">
              <div className={`p-1.5 rounded-md ${
                taskState.isEvaluating || taskState.taskStatusFromBackend === 'EVALUATING'
                  ? 'bg-orange-500/20 dark:bg-orange-400/20'
                  : 'bg-orange-500/10 dark:bg-orange-400/10'
              }`}>
                <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
                {taskState.isEvaluating || taskState.taskStatusFromBackend === 'EVALUATING' ? (
                  <div className="flex items-center">
                    <span className="mr-2">Model Evaluation</span>
                    <div className="flex items-center space-x-1">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      <span className="text-xs text-orange-600 dark:text-orange-400 font-normal ml-1.5">Evaluating...</span>
                    </div>
                  </div>
                ) : (
                  "Model Evaluation"
                )}
              </h2>
            </div>
            <EvaluateArea 
              availableModels={availableModels} 
              onEvaluate={handleEvaluate}
              isLoading={taskState.isEvaluating || taskState.taskStatusFromBackend === 'EVALUATING'}
              disabled={!taskState.outputs || taskState.outputs.length === 0 || taskState.taskStatusFromBackend === 'GENERATING'}
              validOutputsCount={validOutputsCount}
            />
          </div>
        </div>
      )}
      
      {/* Evaluation Results Card - Ultra Compact */}
      {reportToShowForViewer && fullEvaluationToShow && (
        <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
          <div className="px-4 py-4">
            <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3 mb-4">
              <div className="flex items-center gap-2">
                <div className="p-1.5 bg-green-500/10 dark:bg-green-400/10 rounded-md">
                  <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
                    Evaluation Results
                  </h2>
                  <div className="flex items-center gap-1.5 mt-0.5">
                    <span className="text-xs px-2 py-0.5 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle border border-light-border/60 dark:border-dark-border/60">
                      #{fullEvaluationToShow.id}
                    </span>
                    <span className={`text-xs px-1.5 py-0.5 rounded-full font-medium ${fullEvaluationToShow.evaluation_used_blind_ids ? 'bg-blue-100 text-blue-700 dark:bg-blue-700/20 dark:text-blue-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700/20 dark:text-gray-300'}`}>
                      {fullEvaluationToShow.evaluation_used_blind_ids ? "Blind IDs Used" : "Blind IDs Not Used"}
                    </span>
                    {fullEvaluationToShow.evaluation_prompt ? (
                      <span className="text-xs px-1.5 py-0.5 rounded-full font-medium bg-purple-100 text-purple-700 dark:bg-purple-700/20 dark:text-purple-300">
                        Custom Prompt
                      </span>
                    ) : (
                      <span className="text-xs px-1.5 py-0.5 rounded-full font-medium bg-gray-100 text-gray-700 dark:bg-gray-700/20 dark:text-gray-300">
                        Default Prompt
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2 flex-wrap">
                {fullEvaluationToShow?.created_at && (
                  <div className="flex items-center text-xs text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle px-2 py-1 rounded-full">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    {new Date(fullEvaluationToShow.created_at).toLocaleString()}
                  </div>
                )}
                <div className="flex items-center gap-1.5">
                  {/* Dropdown to select Evaluation Batch ID */}
                  {taskState?.evaluations && taskState.evaluations.length > 0 && (
                    <select 
                      value={selectedEvaluationId || ''}
                      onChange={(e) => {
                        setSelectedEvaluationId(parseInt(e.target.value));
                        setSelectedEvaluatorInBatch(null);
                      }}
                      className="text-xs px-2 py-1.5 border border-light-border dark:border-dark-border rounded-md text-light-primary bg-light-component dark:text-dark-primary dark:bg-dark-component focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:border-transparent shadow-sm w-40 max-w-40 truncate"
                    >
                      {taskState.evaluations
                        .sort((a, b) => b.id - a.id) 
                        .map(ev => (
                          <option key={ev.id} value={ev.id} className="bg-light-component dark:bg-dark-component-subtle text-light-primary dark:text-dark-primary truncate">
                            Batch #{ev.id} ({new Date(ev.created_at).toLocaleDateString()})
                          </option>
                        ))}
                    </select>
                  )}
                  {/* Dropdown to select Evaluator within the Batch */}
                  {fullEvaluationToShow && fullEvaluationToShow.rankings && Array.from(new Set(fullEvaluationToShow.rankings.map(r => r.evaluator_model_id))).length > 0 && (
                    <select 
                      value={selectedEvaluatorInBatch || ''}
                      onChange={(e) => setSelectedEvaluatorInBatch(e.target.value || null)}
                      className="text-xs px-2 py-1.5 border border-light-border dark:border-dark-border rounded-md text-light-primary bg-light-component dark:text-dark-primary dark:bg-dark-component focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:border-transparent shadow-sm w-40 max-w-40 truncate"
                    >
                      {Array.from(new Set(fullEvaluationToShow.rankings.map(r => r.evaluator_model_id))).map(evaluatorId => (
                        <option key={evaluatorId} value={evaluatorId} className="bg-light-component dark:bg-dark-component-subtle text-light-primary dark:text-dark-primary truncate">
                          {evaluatorId.split('/').pop()}
                        </option>
                      ))}
                    </select>
                  )}
                  <div className="flex items-center gap-1">
                    <button
                      onClick={handleCopyEvaluationReport}
                      className="group p-2.5 text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary rounded-md hover:bg-light-accent/10 dark:hover:bg-dark-accent/10"
                      title="Copy report for selected evaluator in this batch"
                      disabled={!reportToShowForViewer}
                    >
                      {isReportCopied ? (
                        <Check size={16} className="text-green-500" />
                      ) : (
                        <CopyIcon size={16} />
                      )}
                    </button>
                    {taskState?.evaluations && taskState.evaluations.length > 0 && (
                      <button
                        onClick={handleCopyAllEvaluationReports}
                        className="group p-2.5 text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary rounded-md hover:bg-light-accent/10 dark:hover:bg-dark-accent/10"
                        title="Copy full batch report (all evaluators in this batch)"
                      >
                        {isAllReportsCopied ? (
                          <Check size={16} className="text-green-500" />
                        ) : (
                          <DownloadCloud size={16} />
                        )}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <ReportViewer 
              report={reportToShowForViewer}
              getDisplayId={(genId: number) => getDisplayIdForGenerationInEvaluation(genId, selectedEvaluationId)}
              evaluationUsedBlindIds={fullEvaluationToShow.evaluation_used_blind_ids}
              blindIdToModelNameMap={blindIdToModelNameMap}
              revealGlobalReasoning={revealGlobalReasoning}
              onToggleRevealGlobalReasoning={handleToggleGlobalReasoning}
              evaluationPrompt={fullEvaluationToShow.evaluation_prompt}
            />
          </div>
        </div>
      )}

      {/* Aggregated Evaluation & Consistency - Ultra Compact */}
      {selectedEvaluationId && 
        fullEvaluationToShow && 
        fullEvaluationToShow.status !== 'EVALUATING' &&
        fullEvaluationToShow.status !== 'PENDING' && // PENDING may also mean not ready for aggregation
        taskState?.taskStatusFromBackend !== 'GENERATING' && // Overall task not generating
        taskState?.taskStatusFromBackend !== 'EVALUATING' && // Overall task not evaluating (safety for currentEvaluationId logic)
      (
        <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
          <div className="px-4 py-4">
            <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3 mb-4">
              <div className="flex items-center space-x-2">
                <div className="p-1.5 bg-indigo-500/10 dark:bg-indigo-400/10 rounded-md">
                  <BarChart2 size={16} className="text-indigo-600 dark:text-indigo-400" />
                </div>
                <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
                  Aggregated Evaluation & Consistency
                </h2>
              </div>
              <div className="flex items-center gap-2 flex-wrap">
                <select 
                  value={selectedAggregationAlgorithm}
                  onChange={handleAggregationAlgorithmChange}
                  disabled={taskState.isAggregating}
                  className="text-xs px-2 py-1.5 border border-light-border dark:border-dark-border rounded-md text-light-primary bg-light-component dark:text-dark-primary dark:bg-dark-component focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:border-transparent shadow-sm disabled:opacity-60 w-40 max-w-40 truncate"
                >
                  {Object.values(AggregationAlgorithmEnum).map(algo => (
                    <option key={algo} value={algo} className="bg-light-component dark:bg-dark-component-subtle text-light-primary dark:text-dark-primary truncate">
                      {algo.replace('_', ' ').toUpperCase()} 
                    </option>
                  ))}
                </select>
                <button
                  onClick={handleFetchAggregation}
                  disabled={taskState.isAggregating || !taskState.currentEvaluationId}
                  className={`px-3 py-2 rounded-md text-white font-semibold text-xs focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-dark-component shadow-sm
                    ${
                      taskState.isAggregating || !taskState.currentEvaluationId
                        ? 'bg-light-secondary/70 dark:bg-dark-secondary/70 cursor-not-allowed'
                        : 'bg-gradient-to-r from-light-accent to-indigo-600 dark:from-dark-accent dark:to-indigo-500 hover:from-light-accent-hover hover:to-indigo-700 dark:hover:from-dark-accent-hover dark:hover:to-indigo-400 focus:ring-light-accent dark:focus:ring-dark-accent'
                    }`}
                >
                  {taskState.isAggregating ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-1.5 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Aggregating...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      Calculate Aggregation
                    </div>
                  )}
                </button>
              </div>
            </div>

            {/* Evaluator Weights Input Section - Ultra Compact */}
            {selectedAggregationAlgorithm === AggregationAlgorithmEnum.WEIGHTED_AVERAGE_RANK && uniqueEvaluatorModelIdsForWeightInputs.length > 0 && (
              <div className="mb-4 p-3 rounded-md bg-light-background/50 dark:bg-dark-background/50 border border-light-border/60 dark:border-dark-border/60 shadow-sm">
                <h4 className="text-xs font-semibold text-light-primary dark:text-dark-primary mb-3 flex items-center">
                  <svg className="w-3 h-3 mr-1.5 text-light-accent dark:text-dark-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9" />
                  </svg>
                  Set Evaluator Weights (default: 1.0)
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {uniqueEvaluatorModelIdsForWeightInputs.map(modelId => (
                    <div key={modelId} className="flex items-center gap-2 p-2 bg-light-component dark:bg-dark-component rounded-md">
                      <label htmlFor={`weight-${modelId}`} className="flex-1 text-xs font-medium text-light-primary dark:text-dark-primary truncate" title={modelId}>
                        {modelId.split('/').pop() || modelId}:
                      </label>
                      <input 
                        type="number"
                        id={`weight-${modelId}`}
                        value={evaluatorWeightsInput[modelId] || ''} 
                        onChange={(e) => handleWeightInputChange(modelId, e.target.value)}
                        placeholder="1.0"
                        min="0.01" 
                        step="0.1"
                        className="w-16 text-xs p-1.5 border border-light-border dark:border-dark-border rounded-sm text-light-primary bg-light-component dark:text-dark-primary dark:bg-dark-component focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:border-transparent"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Collapsible Algorithm Description Section - Ultra Compact */}
            {selectedAggregationAlgorithm && algorithmDescriptions[selectedAggregationAlgorithm] && (
              <div className="mt-2 border border-light-border/60 dark:border-dark-border/60 rounded-md shadow-sm bg-light-background/30 dark:bg-dark-background/30">
                <button 
                  onClick={() => handleToggleAlgorithmDescription(selectedAggregationAlgorithm)} // Toggle the selected one
                  className="w-full flex justify-between items-center p-3 text-left text-xs font-medium text-light-primary dark:text-dark-primary bg-light-component-subtle/50 dark:bg-dark-component-subtle/50 hover:bg-light-hover dark:hover:bg-dark-hover focus:outline-none rounded-t-md transition-colors duration-200"
                  aria-expanded={expandedAlgorithmDescription === selectedAggregationAlgorithm}
                  aria-controls={`description-${selectedAggregationAlgorithm}`}
                >
                  <span>Description for: {selectedAggregationAlgorithm.replace('_', ' ').toUpperCase()}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className={`h-3 w-3 transform transition-transform duration-200 ${expandedAlgorithmDescription === selectedAggregationAlgorithm ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {expandedAlgorithmDescription === selectedAggregationAlgorithm && (
                  <div id={`description-${selectedAggregationAlgorithm}`} className="p-3 border-t border-light-border/60 dark:border-dark-border/60 text-xs text-light-secondary dark:text-dark-secondary bg-light-component/30 dark:bg-dark-component/30 whitespace-pre-line rounded-b-md">
                    {algorithmDescriptions[selectedAggregationAlgorithm]}
                  </div>
                )}
              </div>
            )}
            {/* End Collapsible Algorithm Description Section */}

            {taskState.aggregationError && (
              <div className="my-3 p-3 bg-light-error-bg dark:bg-dark-error-bg text-light-error dark:text-dark-error border border-light-error/30 dark:border-dark-error/30 rounded-md shadow-sm">
                <p className="font-medium text-xs">Aggregation Error:</p>
                <p className="text-xs mt-0.5">{taskState.aggregationError}</p>
              </div>
            )}

            {taskState.aggregatedReportsByAlgorithm?.[selectedAggregationAlgorithm] && !taskState.isAggregating && !taskState.aggregationError && (
              <div className="space-y-4 mt-3">
                <div>
                  <h3 className="text-sm font-semibold text-light-primary dark:text-dark-primary mb-2 flex items-center">
                    <BarChart2 size={14} className="mr-1.5" /> Aggregated Model Performance
                  </h3>
                  <AggregatedResultsChart aggregationData={taskState.aggregatedReportsByAlgorithm[selectedAggregationAlgorithm] || null} />
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-light-primary dark:text-dark-primary mb-2 flex items-center">
                    <Users size={14} className="mr-1.5" /> Evaluator Consistency
                  </h3>
                  <EvaluatorConsistencyTable aggregationData={taskState.aggregatedReportsByAlgorithm[selectedAggregationAlgorithm] || null} />
                </div>
              </div>
            )}
            {taskState.isAggregating && (
                 <div className="flex justify-center py-6">
                    <LoadingSpinner message="Calculating aggregated results..." />
                </div>
            )}

          </div>
        </div>
      )}
      
      {/* Evaluation Usage Summary Card */}
      {fullEvaluationToShow && (aggregatedEvaluationUsage && (aggregatedEvaluationUsage.total_tokens > 0 || aggregatedEvaluationUsage.cost_credits !== null) ? (
        <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
          <div className="px-4 py-4">
            <div className="flex items-center space-x-2 mb-4">
              <div className="p-1.5 bg-purple-500/10 dark:bg-purple-400/10 rounded-md">
                <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
                Evaluation Usage Summary
              </h2>
              <span className="text-xs px-2 py-1 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle border border-light-border/60 dark:border-dark-border/60">
                Batch #{fullEvaluationToShow.id}
              </span>
              <span className="text-xs px-2 py-1 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle border border-light-border/60 dark:border-dark-border/60">
                {aggregatedEvaluationUsage.count} evaluator{aggregatedEvaluationUsage.count !== 1 ? 's' : ''} with usage data
              </span>
              {aggregatedEvaluationUsage.count < aggregatedEvaluationUsage.totalEvaluations && (
                <span className="text-xs px-2 py-1 rounded-full text-amber-700 dark:text-amber-300 bg-amber-100 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/60">
                  {aggregatedEvaluationUsage.totalEvaluations - aggregatedEvaluationUsage.count} without usage data
                </span>
              )}
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
              <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Prompt Tokens</div>
                <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                  {aggregatedEvaluationUsage.prompt_tokens.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Completion Tokens</div>
                <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                  {aggregatedEvaluationUsage.completion_tokens.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Total Tokens</div>
                <div className="text-lg font-bold text-purple-600 dark:text-purple-400 font-mono">
                  {aggregatedEvaluationUsage.total_tokens.toLocaleString()}
                </div>
              </div>
              
              {aggregatedEvaluationUsage.reasoning_tokens !== null && aggregatedEvaluationUsage.reasoning_tokens !== undefined && aggregatedEvaluationUsage.reasoning_tokens > 0 && (
                <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Reasoning Tokens</div>
                  <div className="text-lg font-bold text-light-primary dark:text-dark-primary font-mono">
                    {aggregatedEvaluationUsage.reasoning_tokens.toLocaleString()}
                  </div>
                </div>
              )}
              
              {aggregatedEvaluationUsage.cached_tokens !== null && aggregatedEvaluationUsage.cached_tokens !== undefined && aggregatedEvaluationUsage.cached_tokens > 0 && (
                <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                  <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Cached Tokens</div>
                  <div className="text-lg font-bold text-green-600 dark:text-green-400 font-mono">
                    {aggregatedEvaluationUsage.cached_tokens.toLocaleString()}
                  </div>
                </div>
              )}
              
              <div className="bg-light-component dark:bg-dark-component rounded-md p-3 border border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary mb-1">Total Cost</div>
                <div className="text-lg font-bold text-light-accent dark:text-dark-accent font-mono">
                  {aggregatedEvaluationUsage.cost_credits > 0 
                    ? `$${aggregatedEvaluationUsage.cost_credits.toFixed(6)}`
                    : '$ 0.000000'
                  }
                </div>
              </div>
            </div>
            
            {/* Average cost per evaluator */}
            {aggregatedEvaluationUsage.count > 1 && (
              <div className="mt-3 pt-3 border-t border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-light-secondary dark:text-dark-secondary">
                  <span className="font-medium">Average per evaluator:</span>
                  <span className="ml-2 font-mono">
                    {Math.round(aggregatedEvaluationUsage.total_tokens / aggregatedEvaluationUsage.count).toLocaleString()} tokens
                  </span>
                  <span className="mx-2">•</span>
                  <span className="font-mono">
                    ${(aggregatedEvaluationUsage.cost_credits / aggregatedEvaluationUsage.count).toFixed(6)}
                  </span>
                </div>
              </div>
            )}
            
            {/* Note about missing usage data */}
            {aggregatedEvaluationUsage.count < aggregatedEvaluationUsage.totalEvaluations && (
              <div className="mt-3 pt-3 border-t border-light-border/50 dark:border-dark-border/50">
                <div className="text-xs text-amber-700 dark:text-amber-300 bg-amber-50 dark:bg-amber-900/10 p-2 rounded-md border border-amber-200 dark:border-amber-800/30">
                  <div className="flex items-center space-x-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Note:</span>
                    <span>
                      {aggregatedEvaluationUsage.totalEvaluations - aggregatedEvaluationUsage.count} evaluation{aggregatedEvaluationUsage.totalEvaluations - aggregatedEvaluationUsage.count !== 1 ? 's' : ''} 
                      {aggregatedEvaluationUsage.totalEvaluations - aggregatedEvaluationUsage.count === 1 ? ' was' : ' were'} performed before usage tracking was available
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : fullEvaluationToShow.rankings.filter(r => !r.error_message).length > 0 && 
           fullEvaluationToShow.status !== 'EVALUATING' && 
           fullEvaluationToShow.status !== 'PENDING' && (
        /* Show message when no usage statistics are available for any evaluation in this batch - only for completed evaluations */
        <div className="bg-gradient-to-br from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle shadow-md rounded-md border border-light-border/50 dark:border-dark-border/50">
          <div className="px-4 py-4">
            <div className="flex items-center space-x-2 mb-4">
              <div className="p-1.5 bg-gray-500/10 dark:bg-gray-400/10 rounded-md">
                <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-lg font-bold text-light-primary dark:text-dark-primary">
                Evaluation Usage Summary
              </h2>
              <span className="text-xs px-2 py-1 rounded-full text-light-secondary dark:text-dark-secondary bg-light-component-subtle dark:bg-dark-component-subtle border border-light-border/60 dark:border-dark-border/60">
                Batch #{fullEvaluationToShow.id}
              </span>
            </div>
            
            <div className="flex items-center justify-center py-6">
              <div className="text-center">
                <div className="text-sm text-light-secondary dark:text-dark-secondary mb-2">
                  Usage statistics are not available for evaluations in this batch
                </div>
                <div className="text-xs text-light-secondary/70 dark:text-dark-secondary/70">
                  All {fullEvaluationToShow.rankings.filter(r => !r.error_message).length} evaluation{fullEvaluationToShow.rankings.filter(r => !r.error_message).length !== 1 ? 's were' : ' was'} performed before usage tracking was available
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default ViewTaskPage; 